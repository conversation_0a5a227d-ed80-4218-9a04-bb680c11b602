import numpy as np
import pandas as pd
from pathlib import Path
import logging

from constants import AnalysisConstants

logger = logging.getLogger(__name__)

class LabelCreator:
    def __init__(self, data_path=None, correlation_results_path=None, random_seed=42):
        """
        Inisialisasi class untuk membuat label mental well-being berdasarkan data real dan hasil korelasi.

        Parameters:
        - data_path (str): Path ke dataset processed (default: dataset/processed/weekly_merged_dataset_with_gamification.csv)
        - correlation_results_path (str): Path ke hasil korelasi (default: results/reports/all_correlations_comprehensive.csv)
        - random_seed (int): Seed untuk reproduksibilitas.
        """
        self.random_seed = random_seed
        self.results_path = Path(AnalysisConstants.RESULTS_PATH)
        self.results_path.mkdir(parents=True, exist_ok=True)
        np.random.seed(random_seed)

        # Default paths
        if data_path is None:
            data_path = "dataset/processed/weekly_merged_dataset_with_gamification.csv"
        if correlation_results_path is None:
            correlation_results_path = "results/reports/all_correlations_comprehensive.csv"

        self.data_path = Path(data_path)
        self.correlation_results_path = Path(correlation_results_path)

        # Load data
        self.data = self._load_data()
        self.correlation_results = self._load_correlation_results()

        # Results storage
        self.composite_score = None
        self.binary_labels = None
        self.multiclass_labels = None
        self.dataset = None

        # Key variables from correlation analysis
        self.key_variables = self._identify_key_variables()

    def _load_data(self):
        """
        Load processed dataset.
        Returns: pd.DataFrame dengan data processed.
        """
        if not self.data_path.exists():
            logger.error(f"Data file not found: {self.data_path}")
            raise FileNotFoundError(f"Data file not found: {self.data_path}")

        logger.info(f"Loading data from {self.data_path}")
        data = pd.read_csv(self.data_path)
        logger.info(f"Loaded data with shape: {data.shape}")
        return data

    def _load_correlation_results(self):
        """
        Load correlation analysis results.
        Returns: pd.DataFrame dengan hasil korelasi.
        """
        if not self.correlation_results_path.exists():
            logger.warning(f"Correlation results not found: {self.correlation_results_path}")
            return None

        logger.info(f"Loading correlation results from {self.correlation_results_path}")
        corr_results = pd.read_csv(self.correlation_results_path)
        logger.info(f"Loaded {len(corr_results)} correlation results")
        return corr_results

    def _identify_key_variables(self):
        """
        Identify key variables from correlation analysis for mental well-being labeling.
        Returns: dict dengan variabel kunci dan bobotnya.
        """
        if self.correlation_results is None:
            # Default variables if no correlation results
            return {
                'productivity_points': 0.4,
                'activity_days': 0.3,
                'consistency_score': 0.2,
                'achievement_rate': 0.1
            }

        # Find variables most correlated with productivity and well-being indicators
        key_vars = {}

        # Variables that indicate productivity/well-being
        target_vars = ['total_cycles', 'work_days', 'productivity_points']

        for target in target_vars:
            if target in self.data.columns:
                # Find correlations with this target
                target_corrs = self.correlation_results[
                    (self.correlation_results['variable_1'] == target) |
                    (self.correlation_results['variable_2'] == target)
                ].copy()

                # Get top correlated variables
                for _, row in target_corrs.head(10).iterrows():
                    var = row['variable_2'] if row['variable_1'] == target else row['variable_1']
                    if var not in target_vars and var in self.data.columns:
                        weight = abs(row['correlation']) * 0.1  # Scale weight
                        if var in key_vars:
                            key_vars[var] += weight
                        else:
                            key_vars[var] = weight

        # Normalize weights
        total_weight = sum(key_vars.values())
        if total_weight > 0:
            key_vars = {k: v/total_weight for k, v in key_vars.items()}

        logger.info(f"Identified key variables: {key_vars}")
        return key_vars

    def normalize_variables(self, data, variables):
        """
        Normalisasi data untuk variabel yang dipilih.
        Parameters:
        - data (pd.DataFrame): Data yang akan dinormalisasi.
        - variables (list): List variabel yang akan dinormalisasi.
        Returns: pd.DataFrame dengan variabel yang sudah dinormalisasi.
        """
        normalized_data = {}
        for var in variables:
            if var in data.columns:
                mean_val = data[var].mean()
                std_val = data[var].std()
                if std_val > 0:  # Avoid division by zero
                    normalized_data[f'{var}_norm'] = (data[var] - mean_val) / std_val
                else:
                    normalized_data[f'{var}_norm'] = data[var] - mean_val

        return pd.DataFrame(normalized_data)

    def create_composite_score(self, data):
        """
        Membuat skor komposit berdasarkan variabel kunci dari analisis korelasi.
        Parameters:
        - data (pd.DataFrame): Data yang berisi variabel.
        Returns: np.ndarray dengan skor komposit.
        """
        # Normalize key variables
        available_vars = [var for var in self.key_variables.keys() if var in data.columns]

        if not available_vars:
            logger.warning("No key variables found in data. Using default scoring.")
            # Fallback to simple scoring
            if 'productivity_points' in data.columns and 'activity_days' in data.columns:
                prod_norm = (data['productivity_points'] - data['productivity_points'].mean()) / data['productivity_points'].std()
                act_norm = (data['activity_days'] - data['activity_days'].mean()) / data['activity_days'].std()
                return act_norm - prod_norm
            else:
                return np.zeros(len(data))

        normalized_data = self.normalize_variables(data, available_vars)

        # Calculate weighted composite score
        composite_score = np.zeros(len(data))
        for var in available_vars:
            weight = self.key_variables[var]
            norm_var = f'{var}_norm'
            if norm_var in normalized_data.columns:
                # Positive contribution for beneficial variables
                if var in ['activity_days', 'consistency_score', 'achievement_rate', 'activity_points']:
                    composite_score += weight * normalized_data[norm_var]
                # Negative contribution for potentially problematic variables (high productivity without balance)
                elif var in ['productivity_points'] and 'activity_days' in data.columns:
                    # Only negative if activity is low
                    activity_norm = (data['activity_days'] - data['activity_days'].mean()) / data['activity_days'].std()
                    composite_score -= weight * normalized_data[norm_var] * (activity_norm < 0)
                else:
                    composite_score += weight * normalized_data[norm_var]

        return composite_score

    def generate_binary_labels(self, composite_score, threshold_percentile=50):
        """
        Menghasilkan label biner berdasarkan composite score.
        Parameters:
        - composite_score (np.ndarray): Skor komposit.
        - threshold_percentile (float): Persentil untuk threshold (default: median).
        Returns: np.ndarray dengan label biner.
        """
        threshold = np.percentile(composite_score, threshold_percentile)
        return np.where(composite_score >= threshold, "Baik", "Buruk")

    def generate_multiclass_labels(self, composite_score, low_percentile=25, high_percentile=75):
        """
        Menghasilkan label multikelas ('Rendah', 'Sedang', 'Tinggi').
        Parameters:
        - composite_score (np.ndarray): Skor komposit.
        - low_percentile (float): Persentil untuk label 'Rendah'.
        - high_percentile (float): Persentil untuk label 'Tinggi'.
        Returns: np.ndarray dengan label multikelas.
        """
        percentile_25 = np.percentile(composite_score, low_percentile)
        percentile_75 = np.percentile(composite_score, high_percentile)
        return np.where(composite_score < percentile_25, "Rendah",
                        np.where(composite_score > percentile_75, "Tinggi", "Sedang"))

    def create_dataset(self, output_file="mental_well_being_dataset.csv"):
        """
        Membuat dataset lengkap dengan fitur dan label berdasarkan data real dan hasil korelasi.
        Parameters:
        - output_file (str): Nama file output CSV.
        """
        logger.info("Creating mental well-being dataset...")

        # Buat skor komposit dan label
        self.composite_score = self.create_composite_score(self.data)
        self.binary_labels = self.generate_binary_labels(self.composite_score)
        self.multiclass_labels = self.generate_multiclass_labels(self.composite_score)

        # Gabungkan ke DataFrame
        self.dataset = self.data.copy()
        self.dataset['composite_score'] = self.composite_score
        self.dataset['mental_well_being_binary'] = self.binary_labels
        self.dataset['mental_well_being_multiclass'] = self.multiclass_labels

        # Simpan ke CSV
        output_path = self.results_path / output_file
        self.dataset.to_csv(output_path, index=False)
        logger.info(f"Dataset saved to {output_path}")

        return self.dataset

    def get_summary(self):
        """
        Menampilkan distribusi label dan sampel data.
        """
        if self.dataset is None:
            print("Dataset belum dibuat. Jalankan create_dataset() terlebih dahulu.")
            return

        print(f"\n{'='*60}")
        print("MENTAL WELL-BEING DATASET SUMMARY")
        print(f"{'='*60}")

        print(f"\nDataset Shape: {self.dataset.shape}")
        print(f"Key Variables Used: {list(self.key_variables.keys())}")

        print(f"\nComposite Score Statistics:")
        print(f"  Mean: {self.composite_score.mean():.3f}")
        print(f"  Std:  {self.composite_score.std():.3f}")
        print(f"  Min:  {self.composite_score.min():.3f}")
        print(f"  Max:  {self.composite_score.max():.3f}")

        print(f"\nDistribusi Label Biner:")
        binary_counts = self.dataset['mental_well_being_binary'].value_counts()
        for label, count in binary_counts.items():
            percentage = (count / len(self.dataset)) * 100
            print(f"  {label}: {count} ({percentage:.1f}%)")

        print(f"\nDistribusi Label Multikelas:")
        multi_counts = self.dataset['mental_well_being_multiclass'].value_counts()
        for label, count in multi_counts.items():
            percentage = (count / len(self.dataset)) * 100
            print(f"  {label}: {count} ({percentage:.1f}%)")

        print(f"\nSample Data (first 5 rows):")
        display_cols = ['identity', 'composite_score', 'mental_well_being_binary', 'mental_well_being_multiclass']
        available_cols = [col for col in display_cols if col in self.dataset.columns]
        print(self.dataset[available_cols].head())

        print(f"\n{'='*60}")

    def analyze_label_correlations(self):
        """
        Analyze correlations between labels and original variables.
        """
        if self.dataset is None:
            logger.warning("Dataset not created yet. Run create_dataset() first.")
            return

        logger.info("Analyzing label correlations with original variables...")

        # Convert categorical labels to numeric for correlation
        label_numeric = self.dataset['mental_well_being_binary'].map({'Baik': 1, 'Buruk': 0})

        correlations = {}
        numeric_cols = self.dataset.select_dtypes(include=[np.number]).columns

        for col in numeric_cols:
            if col not in ['composite_score']:
                corr = np.corrcoef(self.dataset[col], label_numeric)[0, 1]
                if not np.isnan(corr):
                    correlations[col] = corr

        # Sort by absolute correlation
        sorted_corr = sorted(correlations.items(), key=lambda x: abs(x[1]), reverse=True)

        print(f"\nCorrelations with Mental Well-being Labels:")
        print(f"{'Variable':<25} {'Correlation':<12}")
        print("-" * 40)
        for var, corr in sorted_corr[:10]:  # Top 10
            print(f"{var:<25} {corr:>8.3f}")

# Contoh penggunaan
if __name__ == "__main__":
    # Buat instance class dengan data real
    label_creator = LabelCreator(random_seed=42)

    # Buat dataset
    dataset = label_creator.create_dataset()

    # Tampilkan ringkasan
    label_creator.get_summary()

    # Analisis korelasi label
    label_creator.analyze_label_correlations()