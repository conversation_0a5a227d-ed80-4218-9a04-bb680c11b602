import numpy as np
import pandas as pd
from pathlib import Path
import logging

from constants import AnalysisConstants

logger = logging.getLogger(__name__)

class LabelCreator:
    def __init__(self, data_path=None, correlation_results_path=None, random_seed=42):
        """
        Inisialisasi class untuk membuat label mental well-being berdasarkan data real dan hasil korelasi.

        Parameters:
        - data_path (str): Path ke dataset processed (default: dataset/processed/weekly_merged_dataset_with_gamification.csv)
        - correlation_results_path (str): Path ke hasil korelasi (default: results/reports/all_correlations_comprehensive.csv)
        - random_seed (int): Seed untuk reproduksibilitas.
        """
        self.random_seed = random_seed
        self.results_path = Path(AnalysisConstants.RESULTS_PATH)
        self.results_path.mkdir(parents=True, exist_ok=True)
        np.random.seed(random_seed)

        # Default paths
        if data_path is None:
            data_path = "dataset/processed/weekly_merged_dataset_with_gamification.csv"
        if correlation_results_path is None:
            correlation_results_path = "results/reports/all_correlations_comprehensive.csv"

        self.data_path = Path(data_path)
        self.correlation_results_path = Path(correlation_results_path)

        # Load data
        self.data = self._load_data()
        self.correlation_results = self._load_correlation_results()

        # Results storage
        self.composite_score = None
        self.binary_labels = None
        self.multiclass_labels = None
        self.dataset = None

        # Key variables from correlation analysis
        self.key_variables = self._identify_key_variables()

    def _load_data(self):
        """
        Load processed dataset.
        Returns: pd.DataFrame dengan data processed.
        """
        if not self.data_path.exists():
            logger.error(f"Data file not found: {self.data_path}")
            raise FileNotFoundError(f"Data file not found: {self.data_path}")

        logger.info(f"Loading data from {self.data_path}")
        data = pd.read_csv(self.data_path)
        logger.info(f"Loaded data with shape: {data.shape}")
        return data

    def _load_correlation_results(self):
        """
        Load correlation analysis results.
        Returns: pd.DataFrame dengan hasil korelasi.
        """
        if not self.correlation_results_path.exists():
            logger.warning(f"Correlation results not found: {self.correlation_results_path}")
            return None

        logger.info(f"Loading correlation results from {self.correlation_results_path}")
        corr_results = pd.read_csv(self.correlation_results_path)
        logger.info(f"Loaded {len(corr_results)} correlation results")
        return corr_results

    def _identify_key_variables(self):
        """
        Identify key variables from correlation analysis for mental well-being labeling.
        Avoids redundant variables by checking inter-correlations.
        Returns: dict dengan variabel kunci dan bobotnya.
        """
        if self.correlation_results is None:
            # Default variables if no correlation results
            return {
                'activity_days': 0.4,
                'gamification_balance': 0.3,
                'total_distance_km': 0.3
            }

        # Step 1: Find all variables correlated with well-being indicators
        candidate_vars = {}
        target_vars = ['total_cycles', 'work_days', 'productivity_points']

        for target in target_vars:
            if target in self.data.columns:
                target_corrs = self.correlation_results[
                    (self.correlation_results['variable_1'] == target) |
                    (self.correlation_results['variable_2'] == target)
                ].copy()

                for _, row in target_corrs.iterrows():
                    var = row['variable_2'] if row['variable_1'] == target else row['variable_1']
                    if (var not in target_vars and var in self.data.columns and
                        abs(row['correlation']) >= 0.3 and row['significant']):

                        if var in candidate_vars:
                            candidate_vars[var] = max(candidate_vars[var], abs(row['correlation']))
                        else:
                            candidate_vars[var] = abs(row['correlation'])

        # Step 2: Remove redundant variables (highly correlated with each other)
        final_vars = {}
        sorted_candidates = sorted(candidate_vars.items(), key=lambda x: x[1], reverse=True)

        for var, strength in sorted_candidates:
            is_redundant = False

            # Check if this variable is highly correlated with already selected variables
            for selected_var in final_vars.keys():
                var_corr = self._get_correlation_between_vars(var, selected_var)
                if abs(var_corr) > 0.7:  # High correlation threshold
                    logger.info(f"Skipping {var} due to high correlation ({var_corr:.3f}) with {selected_var}")
                    is_redundant = True
                    break

            if not is_redundant:
                final_vars[var] = strength

            # Limit to top 4 variables to avoid overfitting
            if len(final_vars) >= 4:
                break

        # Step 3: Normalize weights and ensure diversity
        if final_vars:
            total_weight = sum(final_vars.values())
            final_vars = {k: v/total_weight for k, v in final_vars.items()}
        else:
            # Fallback to diverse variables
            final_vars = {
                'activity_days': 0.4,
                'gamification_balance': 0.3,
                'total_distance_km': 0.3
            }

        logger.info(f"Selected non-redundant variables: {final_vars}")
        return final_vars

    def _get_correlation_between_vars(self, var1: str, var2: str) -> float:
        """Get correlation coefficient between two variables."""
        corr_row = self.correlation_results[
            ((self.correlation_results['variable_1'] == var1) & (self.correlation_results['variable_2'] == var2)) |
            ((self.correlation_results['variable_1'] == var2) & (self.correlation_results['variable_2'] == var1))
        ]

        if not corr_row.empty:
            return corr_row.iloc[0]['correlation']
        return 0.0

    def normalize_variables(self, data, variables):
        """
        Normalisasi data untuk variabel yang dipilih.
        Parameters:
        - data (pd.DataFrame): Data yang akan dinormalisasi.
        - variables (list): List variabel yang akan dinormalisasi.
        Returns: pd.DataFrame dengan variabel yang sudah dinormalisasi.
        """
        normalized_data = {}
        for var in variables:
            if var in data.columns:
                mean_val = data[var].mean()
                std_val = data[var].std()
                if std_val > 0:  # Avoid division by zero
                    normalized_data[f'{var}_norm'] = (data[var] - mean_val) / std_val
                else:
                    normalized_data[f'{var}_norm'] = data[var] - mean_val

        return pd.DataFrame(normalized_data)

    def create_composite_score(self, data):
        """
        Membuat skor komposit berdasarkan variabel kunci dari analisis korelasi.
        Parameters:
        - data (pd.DataFrame): Data yang berisi variabel.
        Returns: np.ndarray dengan skor komposit.
        """
        # Normalize key variables
        available_vars = [var for var in self.key_variables.keys() if var in data.columns]

        if not available_vars:
            logger.warning("No key variables found in data. Using default scoring.")
            # Fallback to simple scoring
            if 'productivity_points' in data.columns and 'activity_days' in data.columns:
                prod_norm = (data['productivity_points'] - data['productivity_points'].mean()) / data['productivity_points'].std()
                act_norm = (data['activity_days'] - data['activity_days'].mean()) / data['activity_days'].std()
                return act_norm - prod_norm
            else:
                return np.zeros(len(data))

        normalized_data = self.normalize_variables(data, available_vars)

        # Calculate weighted composite score
        composite_score = np.zeros(len(data))
        for var in available_vars:
            weight = self.key_variables[var]
            norm_var = f'{var}_norm'
            if norm_var in normalized_data.columns:
                # Positive contribution for beneficial variables
                if var in ['activity_days', 'consistency_score', 'achievement_rate', 'activity_points']:
                    composite_score += weight * normalized_data[norm_var]
                # Negative contribution for potentially problematic variables (high productivity without balance)
                elif var in ['productivity_points'] and 'activity_days' in data.columns:
                    # Only negative if activity is low
                    activity_norm = (data['activity_days'] - data['activity_days'].mean()) / data['activity_days'].std()
                    composite_score -= weight * normalized_data[norm_var] * (activity_norm < 0)
                else:
                    composite_score += weight * normalized_data[norm_var]

        return composite_score

    def generate_binary_labels(self, composite_score, threshold_percentile=50):
        """
        Menghasilkan label biner berdasarkan composite score.
        Parameters:
        - composite_score (np.ndarray): Skor komposit.
        - threshold_percentile (float): Persentil untuk threshold (default: median).
        Returns: np.ndarray dengan label biner.
        """
        threshold = np.percentile(composite_score, threshold_percentile)
        return np.where(composite_score >= threshold, "Baik", "Buruk")

    def generate_multiclass_labels(self, composite_score, low_percentile=25, high_percentile=75):
        """
        Menghasilkan label multikelas ('Rendah', 'Sedang', 'Tinggi').
        Parameters:
        - composite_score (np.ndarray): Skor komposit.
        - low_percentile (float): Persentil untuk label 'Rendah'.
        - high_percentile (float): Persentil untuk label 'Tinggi'.
        Returns: np.ndarray dengan label multikelas.
        """
        percentile_25 = np.percentile(composite_score, low_percentile)
        percentile_75 = np.percentile(composite_score, high_percentile)
        return np.where(composite_score < percentile_25, "Rendah",
                        np.where(composite_score > percentile_75, "Tinggi", "Sedang"))

    def create_dataset(self, output_file="mental_well_being_dataset.csv"):
        """
        Membuat dataset lengkap dengan fitur dan label berdasarkan data real dan hasil korelasi.
        Parameters:
        - output_file (str): Nama file output CSV.
        """
        logger.info("Creating mental well-being dataset...")

        # Buat skor komposit dan label
        self.composite_score = self.create_composite_score(self.data)
        self.binary_labels = self.generate_binary_labels(self.composite_score)
        self.multiclass_labels = self.generate_multiclass_labels(self.composite_score)

        # Gabungkan ke DataFrame
        self.dataset = self.data.copy()
        self.dataset['composite_score'] = self.composite_score
        self.dataset['mental_well_being_binary'] = self.binary_labels
        self.dataset['mental_well_being_multiclass'] = self.multiclass_labels

        # Simpan ke CSV
        output_path = self.results_path / output_file
        self.dataset.to_csv(output_path, index=False)
        logger.info(f"Dataset saved to {output_path}")

        return self.dataset

    def get_summary(self):
        """
        Menampilkan distribusi label dan sampel data.
        """
        if self.dataset is None:
            print("Dataset belum dibuat. Jalankan create_dataset() terlebih dahulu.")
            return

        print(f"\n{'='*60}")
        print("MENTAL WELL-BEING DATASET SUMMARY")
        print(f"{'='*60}")

        print(f"\nDataset Shape: {self.dataset.shape}")
        print(f"Key Variables Used: {list(self.key_variables.keys())}")

        print(f"\nComposite Score Statistics:")
        print(f"  Mean: {self.composite_score.mean():.3f}")
        print(f"  Std:  {self.composite_score.std():.3f}")
        print(f"  Min:  {self.composite_score.min():.3f}")
        print(f"  Max:  {self.composite_score.max():.3f}")

        print(f"\nDistribusi Label Biner:")
        binary_counts = self.dataset['mental_well_being_binary'].value_counts()
        for label, count in binary_counts.items():
            percentage = (count / len(self.dataset)) * 100
            print(f"  {label}: {count} ({percentage:.1f}%)")

        print(f"\nDistribusi Label Multikelas:")
        multi_counts = self.dataset['mental_well_being_multiclass'].value_counts()
        for label, count in multi_counts.items():
            percentage = (count / len(self.dataset)) * 100
            print(f"  {label}: {count} ({percentage:.1f}%)")

        print(f"\nSample Data (first 5 rows):")
        display_cols = ['identity', 'composite_score', 'mental_well_being_binary', 'mental_well_being_multiclass']
        available_cols = [col for col in display_cols if col in self.dataset.columns]
        print(self.dataset[available_cols].head())

        print(f"\n{'='*60}")

    def analyze_label_correlations(self):
        """
        Analyze correlations between labels and original variables.
        """
        if self.dataset is None:
            logger.warning("Dataset not created yet. Run create_dataset() first.")
            return

        logger.info("Analyzing label correlations with original variables...")

        # Convert categorical labels to numeric for correlation
        label_numeric = self.dataset['mental_well_being_binary'].map({'Baik': 1, 'Buruk': 0})

        correlations = {}
        numeric_cols = self.dataset.select_dtypes(include=[np.number]).columns

        for col in numeric_cols:
            if col not in ['composite_score']:
                corr = np.corrcoef(self.dataset[col], label_numeric)[0, 1]
                if not np.isnan(corr):
                    correlations[col] = corr

        # Sort by absolute correlation
        sorted_corr = sorted(correlations.items(), key=lambda x: abs(x[1]), reverse=True)

        print(f"\nCorrelations with Mental Well-being Labels:")
        print(f"{'Variable':<25} {'Correlation':<12}")
        print("-" * 40)
        for var, corr in sorted_corr[:10]:  # Top 10
            print(f"{var:<25} {corr:>8.3f}")

    def compare_feature_sets(self):
        """
        Compare current features vs proposed features for mental well-being labeling.
        """
        # Proposed features by user
        proposed_features = [
            'productivity_points', 'activity_days', 'consistency_score',
            'achievement_rate', 'activity_points', 'total_distance_km',
            'total_sport_score', 'gamification_balance'
        ]

        # Current features
        current_features = list(self.key_variables.keys())

        print(f"\n{'='*80}")
        print("PERBANDINGAN FITUR UNTUK MENTAL WELL-BEING LABELING")
        print(f"{'='*80}")

        print(f"\n🔵 FITUR SAAT INI ({len(current_features)} fitur):")
        for i, feature in enumerate(current_features, 1):
            weight = self.key_variables[feature]
            print(f"  {i}. {feature:<25} (bobot: {weight:.3f})")

        print(f"\n🟡 FITUR YANG DIUSULKAN ({len(proposed_features)} fitur):")
        for i, feature in enumerate(proposed_features, 1):
            status = "✅ (digunakan)" if feature in current_features else "❌ (tidak digunakan)"
            print(f"  {i}. {feature:<25} {status}")

        # Analyze redundancy in proposed features
        print(f"\n🔍 ANALISIS REDUNDANSI FITUR YANG DIUSULKAN:")
        self._analyze_redundancy_in_proposed_features(proposed_features)

        # Create alternative labeling with proposed features
        print(f"\n🧪 EKSPERIMEN: LABELING DENGAN FITUR YANG DIUSULKAN")
        self._experiment_with_proposed_features(proposed_features)

    def _analyze_redundancy_in_proposed_features(self, proposed_features):
        """Analyze redundancy in proposed features."""
        if self.correlation_results is None:
            print("   ⚠️ Tidak ada data korelasi untuk analisis redundansi")
            return

        redundant_pairs = []
        moderate_pairs = []

        for i, var1 in enumerate(proposed_features):
            for j, var2 in enumerate(proposed_features):
                if i < j:
                    corr_val = self._get_correlation_between_vars(var1, var2)
                    if abs(corr_val) > 0.7:
                        redundant_pairs.append((var1, var2, corr_val))
                    elif abs(corr_val) > 0.3:
                        moderate_pairs.append((var1, var2, corr_val))

        print(f"\n   ❌ PASANGAN REDUNDAN (|r| > 0.7): {len(redundant_pairs)} pasangan")
        for var1, var2, corr in redundant_pairs:
            print(f"      {var1} ↔ {var2}: r = {corr:.3f}")

        print(f"\n   ⚠️ PASANGAN MODERATE (0.3 < |r| ≤ 0.7): {len(moderate_pairs)} pasangan")
        for var1, var2, corr in moderate_pairs[:5]:  # Show top 5
            print(f"      {var1} ↔ {var2}: r = {corr:.3f}")
        if len(moderate_pairs) > 5:
            print(f"      ... dan {len(moderate_pairs)-5} pasangan lainnya")

    def _experiment_with_proposed_features(self, proposed_features):
        """Experiment with creating labels using proposed features."""
        # Filter available features
        available_features = [f for f in proposed_features if f in self.data.columns]

        print(f"   📊 Fitur tersedia: {len(available_features)}/{len(proposed_features)}")

        # Create composite score with all proposed features (equal weights)
        equal_weights = {f: 1.0/len(available_features) for f in available_features}

        # Normalize and create composite score
        normalized_data = self.normalize_variables(self.data, available_features)
        composite_score_proposed = np.zeros(len(self.data))

        for var in available_features:
            norm_var = f'{var}_norm'
            if norm_var in normalized_data.columns:
                # Positive contribution for beneficial variables
                if var in ['activity_days', 'consistency_score', 'achievement_rate',
                          'activity_points', 'total_distance_km', 'total_sport_score']:
                    composite_score_proposed += equal_weights[var] * normalized_data[norm_var]
                # Negative contribution for potentially problematic variables
                elif var in ['productivity_points']:
                    composite_score_proposed -= equal_weights[var] * normalized_data[norm_var]
                # Balance variable
                elif var == 'gamification_balance':
                    composite_score_proposed += equal_weights[var] * normalized_data[norm_var]

        # Generate labels
        binary_labels_proposed = self.generate_binary_labels(composite_score_proposed)
        multiclass_labels_proposed = self.generate_multiclass_labels(composite_score_proposed)

        # Compare distributions
        print(f"\n   📈 PERBANDINGAN DISTRIBUSI LABEL:")

        # Current distribution
        current_binary = self.dataset['mental_well_being_binary'].value_counts()
        current_multi = self.dataset['mental_well_being_multiclass'].value_counts()

        # Proposed distribution
        proposed_binary = pd.Series(binary_labels_proposed).value_counts()
        proposed_multi = pd.Series(multiclass_labels_proposed).value_counts()

        print(f"\n      BINARY LABELS:")
        print(f"      {'Label':<10} {'Current':<10} {'Proposed':<10} {'Difference':<10}")
        print(f"      {'-'*45}")
        for label in ['Baik', 'Buruk']:
            curr = current_binary.get(label, 0)
            prop = proposed_binary.get(label, 0)
            diff = prop - curr
            print(f"      {label:<10} {curr:<10} {prop:<10} {diff:+d}")

        print(f"\n      MULTICLASS LABELS:")
        print(f"      {'Label':<10} {'Current':<10} {'Proposed':<10} {'Difference':<10}")
        print(f"      {'-'*45}")
        for label in ['Rendah', 'Sedang', 'Tinggi']:
            curr = current_multi.get(label, 0)
            prop = proposed_multi.get(label, 0)
            diff = prop - curr
            print(f"      {label:<10} {curr:<10} {prop:<10} {diff:+d}")

# Contoh penggunaan
if __name__ == "__main__":
    # Buat instance class dengan data real
    label_creator = LabelCreator(random_seed=42)

    # Buat dataset dengan fitur saat ini
    dataset = label_creator.create_dataset()

    # Tampilkan ringkasan
    label_creator.get_summary()

    # Analisis korelasi label
    label_creator.analyze_label_correlations()

    # Bandingkan dengan fitur yang diusulkan
    label_creator.compare_feature_sets()