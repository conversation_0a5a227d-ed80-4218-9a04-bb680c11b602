import numpy as np
import pandas as pd
from pathlib import Path

from constants import AnalysisConstants

class LabelCreator:
    def __init__(self, n_samples=300, data=None, random_seed=42):
        """
        Inisialisasi class untuk membuat label mental well-being.
        
        Parameters:
        - n_samples (int): <PERSON><PERSON><PERSON> sampel untuk simulasi (default: 300).
        - data (pd.DataFrame): Data nyata (opsional). Jika None, simulasi digunakan.
        - random_seed (int): Seed untuk reproduksibilitas.
        """
        self.n_samples = n_samples
        self.data = data
        self.random_seed = random_seed
        self.results_path = Path(AnalysisConstants.RESULTS_PATH)
        self.results_path.mkdir(parents=True, exist_ok=True)
        np.random.seed(random_seed)
        
        # Parameter statistik dari CSV
        self.stats = {
            'productivity_points': {'mean': 50.266666666666666, 'std': 28.821829952803423},
            'activity_days': {'mean': 1.5633333333333332, 'std': 0.779753555670524},
            'consistency_score': {'mean': 0.6113333333333334, 'std': 0.22128740574652905},
            'achievement_rate': {'mean': 0.6661944444444445, 'std': 0.2005520279405361},
            'activity_points': {'mean': 82.97222222222223, 'std': 22.01459700085088},
            'total_distance_km': {'mean': 7.697999999999999, 'std': 5.803204857841362},
            'total_sport_score': {'mean': 2.86, 'std': 2.200729584101858},
            'gamification_balance': {'mean': 37.00555555555556, 'std': 26.852854173854574}
        }
        
        self.composite_score = None
        self.binary_labels = None
        self.multiclass_labels = None
        self.dataset = None

    def simulate_data(self):
        """
        Simulasi data berdasarkan mean dan std dari CSV.
        Returns: pd.DataFrame dengan data simulasi.
        """
        data_dict = {}
        for var, params in self.stats.items():
            data_dict[var] = np.random.normal(params['mean'], params['std'], self.n_samples)
            # Pastikan variabel tertentu tidak negatif
            if var in ['activity_days', 'activity_points', 'total_distance_km', 'total_sport_score']:
                data_dict[var] = np.clip(data_dict[var], 0, None)
        
        return pd.DataFrame(data_dict)

    def normalize_data(self, data):
        """
        Normalisasi data untuk variabel tertentu.
        Parameters:
        - data (pd.DataFrame): Data yang akan dinormalisasi.
        Returns: Tuple (productivity_normalized, activity_days_normalized).
        """
        productivity_normalized = (
            (data['productivity_points'] - self.stats['productivity_points']['mean']) /
            self.stats['productivity_points']['std']
        )
        activity_days_normalized = (
            (data['activity_days'] - self.stats['activity_days']['mean']) /
            self.stats['activity_days']['std']
        )
        return productivity_normalized, activity_days_normalized

    def create_composite_score(self, data):
        """
        Membuat skor komposit dari productivity_points dan activity_days.
        Parameters:
        - data (pd.DataFrame): Data yang berisi variabel.
        Returns: np.ndarray dengan skor komposit.
        """
        productivity_normalized, activity_days_normalized = self.normalize_data(data)
        # Skor = aktivitas tinggi (positif) - produktivitas tinggi (negatif)
        return activity_days_normalized - productivity_normalized

    def generate_binary_labels(self, data, prod_percentile=75, act_percentile=25):
        """
        Menghasilkan label biner ('Baik', 'Buruk').
        Parameters:
        - data (pd.DataFrame): Data yang berisi variabel.
        - prod_percentile (float): Persentil untuk productivity_points.
        - act_percentile (float): Persentil untuk activity_days.
        Returns: np.ndarray dengan label biner.
        """
        return np.where(
            (data['productivity_points'] > np.percentile(data['productivity_points'], prod_percentile)) &
            (data['activity_days'] < np.percentile(data['activity_days'], act_percentile)),
            "Buruk", "Baik"
        )

    def generate_multiclass_labels(self, composite_score, low_percentile=25, high_percentile=75):
        """
        Menghasilkan label multikelas ('Rendah', 'Sedang', 'Tinggi').
        Parameters:
        - composite_score (np.ndarray): Skor komposit.
        - low_percentile (float): Persentil untuk label 'Rendah'.
        - high_percentile (float): Persentil untuk label 'Tinggi'.
        Returns: np.ndarray dengan label multikelas.
        """
        percentile_25 = np.percentile(composite_score, low_percentile)
        percentile_75 = np.percentile(composite_score, high_percentile)
        return np.where(composite_score < percentile_25, "Rendah",
                        np.where(composite_score > percentile_75, "Tinggi", "Sedang"))

    def create_dataset(self, output_file="mental_well_being_dataset.csv"):
        """
        Membuat dataset lengkap dengan fitur dan label, lalu menyimpan ke CSV.
        Parameters:
        - output_file (str): Nama file output CSV.
        """
        # Gunakan data nyata atau simulasi
        data = self.data if self.data is not None else self.simulate_data()
        
        # Buat skor komposit dan label
        self.composite_score = self.create_composite_score(data)
        self.binary_labels = self.generate_binary_labels(data)
        self.multiclass_labels = self.generate_multiclass_labels(self.composite_score)
        
        # Gabungkan ke DataFrame
        self.dataset = data.copy()
        self.dataset['mental_well_being_binary'] = self.binary_labels
        self.dataset['mental_well_being_multiclass'] = self.multiclass_labels
        
        # Simpan ke CSV
        self.dataset.to_csv(self.results_path / output_file, index=False)

    def get_summary(self):
        """
        Menampilkan distribusi label dan sampel data.
        """
        if self.dataset is None:
            print("Dataset belum dibuat. Jalankan create_dataset() terlebih dahulu.")
            return
        
        print("Distribusi Label Biner:")
        print(self.dataset['mental_well_being_binary'].value_counts())
        print("\nDistribusi Label Multikelas:")
        print(self.dataset['mental_well_being_multiclass'].value_counts())
        print("\nSample Data:")
        print(self.dataset.head())

# Contoh penggunaan
if __name__ == "__main__":
    # Buat instance class
    label_creator = LabelCreator(n_samples=300, random_seed=42)
    
    # Buat dataset
    label_creator.create_dataset()
    
    # Tampilkan ringkasan
    label_creator.get_summary()