Stack trace:
Frame         Function      Args
0007FFFFB6A0  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFFA5A0) msys-2.0.dll+0x2118E
0007FFFFB6A0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFB6A0  0002100469F2 (00021028DF99, 0007FFFFB558, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFB6A0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFB6A0  00021006A545 (0007FFFFB6B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFFB6B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF80D4D0000 ntdll.dll
7FF80C3D0000 KERNEL32.DLL
7FF80A5F0000 KERNELBASE.dll
7FF80B4C0000 USER32.dll
7FF80A5C0000 win32u.dll
7FF80BAF0000 GDI32.dll
7FF80B030000 gdi32full.dll
7FF80AC40000 msvcp_win.dll
7FF80AE20000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF80CAF0000 advapi32.dll
7FF80B360000 msvcrt.dll
7FF80C600000 sechost.dll
7FF80B000000 bcrypt.dll
7FF80CBB0000 RPCRT4.dll
7FF809CC0000 CRYPTBASE.DLL
7FF80ABC0000 bcryptPrimitives.dll
7FF80BA90000 IMM32.DLL
