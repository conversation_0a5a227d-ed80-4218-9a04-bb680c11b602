"""
Correlation Analysis Module
Enhanced implementation for comprehensive correlation analysis across all features
"""

import pandas as pd
import numpy as np
from scipy.stats import pearson<PERSON>, spearmanr
from typing import Dict, List, Any, Tuple
import logging
from pathlib import Path
import seaborn as sns
import matplotlib.pyplot as plt

from constants import AnalysisConstants

logger = logging.getLogger(__name__)

class CorrelationAnalyzer:
    """
    Clean correlation analysis class following SOLID principles
    """
    
    def __init__(self, significance_level: float = AnalysisConstants.DEFAULT_SIGNIFICANCE_LEVEL):
        """
        Initialize correlation analyzer

        Args:
            significance_level: Statistical significance threshold
        """
        self.significance_level = significance_level
        self.results_path = Path(AnalysisConstants.RESULTS_PATH)
        self.results_path.mkdir(parents=True, exist_ok=True)

    def analyze_all_correlations(self, data: pd.DataFrame,
                                correlation_method: str = 'pearson',
                                exclude_columns: List[str] = None) -> pd.DataFrame:
        """
        Analyze correlations between ALL numeric features in the dataset

        Args:
            data: Input dataset
            correlation_method: 'pearson' or 'spearman'
            exclude_columns: List of columns to exclude from analysis

        Returns:
            DataFrame with all pairwise correlations
        """
        logger.info(f"Analyzing ALL pairwise correlations using {correlation_method} method...")

        # Get numeric columns
        numeric_columns = data.select_dtypes(include=[np.number]).columns.tolist()

        # Remove excluded columns
        if exclude_columns:
            numeric_columns = [col for col in numeric_columns if col not in exclude_columns]

        # Remove columns with constant values
        numeric_columns = [col for col in numeric_columns if data[col].nunique() > 1]

        logger.info(f"Found {len(numeric_columns)} numeric features for correlation analysis")

        results = []

        # Analyze all pairwise combinations
        for i, var1 in enumerate(numeric_columns):
            for j, var2 in enumerate(numeric_columns):
                if i != j:  # Skip self-correlation
                    result = self._calculate_correlation_enhanced(
                        data, var1, var2, correlation_method
                    )
                    if result:
                        results.append(result)

        results_df = pd.DataFrame(results)

        if not results_df.empty:
            # Sort by absolute correlation strength
            results_df = results_df.reindex(
                results_df['correlation'].abs().sort_values(ascending=False).index
            )

            # Add ranking
            results_df['rank'] = range(1, len(results_df) + 1)

        logger.info(f"Completed analysis: {len(results_df)} correlations, "
                   f"{len(results_df[results_df['significant']])} significant")

        return results_df

    def _calculate_correlation_enhanced(self, data: pd.DataFrame,
                                      var1: str, var2: str,
                                      method: str = 'pearson') -> Dict[str, Any]:
        """
        Calculate enhanced correlation between two variables

        Args:
            data: Input dataset
            var1: First variable name
            var2: Second variable name
            method: Correlation method ('pearson' or 'spearman')

        Returns:
            Dictionary with enhanced correlation results
        """
        # Clean data (remove missing values)
        clean_data = data[[var1, var2]].dropna()

        if len(clean_data) < AnalysisConstants.MINIMUM_SAMPLE_SIZE:
            logger.warning(f"Insufficient data for {var1} vs {var2}")
            return None

        # Calculate correlation based on method
        if method == 'pearson':
            r, p = pearsonr(clean_data[var1], clean_data[var2])
        elif method == 'spearman':
            r, p = spearmanr(clean_data[var1], clean_data[var2])
        else:
            raise ValueError("Method must be 'pearson' or 'spearman'")

        # Calculate additional statistics
        var1_mean = clean_data[var1].mean()
        var2_mean = clean_data[var2].mean()
        var1_std = clean_data[var1].std()
        var2_std = clean_data[var2].std()

        # Effect size (Cohen's conventions for correlation)
        effect_size = self._interpret_effect_size(abs(r))

        # Confidence interval (approximate for large samples)
        ci_lower, ci_upper = self._calculate_correlation_ci(r, len(clean_data))

        # Interpret strength and direction
        strength = self._interpret_correlation_strength(abs(r))
        direction = "positive" if r > 0 else "negative"
        significant = p < self.significance_level

        return {
            'variable_1': var1,
            'variable_2': var2,
            'correlation': r,
            'correlation_abs': abs(r),
            'p_value': p,
            'sample_size': len(clean_data),
            'strength': strength,
            'direction': direction,
            'significant': significant,
            'effect_size': effect_size,
            'method': method,
            'var1_mean': var1_mean,
            'var2_mean': var2_mean,
            'var1_std': var1_std,
            'var2_std': var2_std,
            'ci_lower': ci_lower,
            'ci_upper': ci_upper,
            'missing_values': len(data) - len(clean_data)
        }

    def _interpret_effect_size(self, abs_r: float) -> str:
        """Interpret effect size based on Cohen's conventions"""
        if abs_r >= 0.5:
            return "large"
        elif abs_r >= 0.3:
            return "medium"
        elif abs_r >= 0.1:
            return "small"
        else:
            return "negligible"

    def _calculate_correlation_ci(self, r: float, n: int, confidence: float = 0.95) -> Tuple[float, float]:
        """Calculate confidence interval for correlation coefficient"""
        if n < 4:
            return (np.nan, np.nan)

        # Fisher's z-transformation
        z = 0.5 * np.log((1 + r) / (1 - r))
        se = 1 / np.sqrt(n - 3)

        # Critical value for confidence interval
        from scipy.stats import norm
        alpha = 1 - confidence
        z_critical = norm.ppf(1 - alpha/2)

        # Confidence interval in z-space
        z_lower = z - z_critical * se
        z_upper = z + z_critical * se

        # Transform back to correlation space
        r_lower = (np.exp(2 * z_lower) - 1) / (np.exp(2 * z_lower) + 1)
        r_upper = (np.exp(2 * z_upper) - 1) / (np.exp(2 * z_upper) + 1)

        return (r_lower, r_upper)
    
    def _interpret_correlation_strength(self, abs_r: float) -> str:
        """Interpret correlation strength based on absolute value"""
        if abs_r >= AnalysisConstants.VERY_LARGE_THRESHOLD:
            return "very_large"
        elif abs_r >= AnalysisConstants.LARGE_THRESHOLD:
            return "large"
        elif abs_r >= AnalysisConstants.MEDIUM_THRESHOLD:
            return "medium"
        elif abs_r >= AnalysisConstants.SMALL_THRESHOLD:
            return "small"
        else:
            return "negligible"
    


    def create_correlation_matrix_report(self, data: pd.DataFrame,
                                       exclude_columns: List[str] = None) -> pd.DataFrame:
        """
        Create correlation matrix report for all numeric features

        Args:
            data: Input dataset
            exclude_columns: List of columns to exclude

        Returns:
            Correlation matrix as DataFrame
        """
        logger.info("Creating correlation matrix report...")

        # Get numeric columns
        numeric_columns = data.select_dtypes(include=[np.number]).columns.tolist()

        # Remove excluded columns
        if exclude_columns:
            numeric_columns = [col for col in numeric_columns if col not in exclude_columns]

        # Remove columns with constant values
        numeric_columns = [col for col in numeric_columns if data[col].nunique() > 1]

        # Calculate correlation matrix
        correlation_matrix = data[numeric_columns].corr()

        return correlation_matrix

    def create_detailed_correlation_report(self, all_correlations: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """
        Create detailed correlation reports with various filters and summaries

        Args:
            all_correlations: DataFrame with all correlation results

        Returns:
            Dictionary with different report views
        """
        logger.info("Creating detailed correlation reports...")

        reports = {}

        # 1. Significant correlations only
        reports['significant'] = all_correlations[
            all_correlations['significant'] == True
        ].copy()

        # 2. Strong correlations (>0.5)
        reports['strong'] = all_correlations[
            all_correlations['correlation_abs'] >= 0.5
        ].copy()

        # 3. Medium to strong correlations (>0.3)
        reports['medium_plus'] = all_correlations[
            all_correlations['correlation_abs'] >= 0.3
        ].copy()

        # 4. Top 50 strongest correlations
        reports['top_50'] = all_correlations.head(50).copy()

        # 5. Positive correlations only
        reports['positive'] = all_correlations[
            all_correlations['direction'] == 'positive'
        ].copy()

        # 6. Negative correlations only
        reports['negative'] = all_correlations[
            all_correlations['direction'] == 'negative'
        ].copy()

        # 7. Summary by variable (how many significant correlations each variable has)
        reports['variable_summary'] = self._create_variable_summary(all_correlations)

        # 8. Summary by strength category
        reports['strength_summary'] = self._create_strength_summary(all_correlations)

        return reports

    def _create_variable_summary(self, all_correlations: pd.DataFrame) -> pd.DataFrame:
        """Create summary of correlations by variable"""

        # Count correlations for each variable as variable_1
        var1_counts = all_correlations.groupby('variable_1').agg({
            'correlation': 'count',
            'significant': 'sum',
            'correlation_abs': ['mean', 'max']
        }).round(3)

        # Count correlations for each variable as variable_2
        var2_counts = all_correlations.groupby('variable_2').agg({
            'correlation': 'count',
            'significant': 'sum',
            'correlation_abs': ['mean', 'max']
        }).round(3)

        # Flatten column names
        var1_counts.columns = ['total_as_var1', 'significant_as_var1', 'avg_abs_corr_as_var1', 'max_abs_corr_as_var1']
        var2_counts.columns = ['total_as_var2', 'significant_as_var2', 'avg_abs_corr_as_var2', 'max_abs_corr_as_var2']

        # Combine and calculate totals
        summary = pd.concat([var1_counts, var2_counts], axis=1, sort=False).fillna(0)
        summary['total_correlations'] = summary['total_as_var1'] + summary['total_as_var2']
        summary['total_significant'] = summary['significant_as_var1'] + summary['significant_as_var2']
        summary['significance_rate'] = (summary['total_significant'] / summary['total_correlations'] * 100).round(1)
        summary['avg_abs_correlation'] = ((summary['avg_abs_corr_as_var1'] + summary['avg_abs_corr_as_var2']) / 2).round(3)
        summary['max_abs_correlation'] = np.maximum(summary['max_abs_corr_as_var1'], summary['max_abs_corr_as_var2']).round(3)

        # Sort by total significant correlations
        summary = summary.sort_values('total_significant', ascending=False)

        return summary

    def _create_strength_summary(self, all_correlations: pd.DataFrame) -> pd.DataFrame:
        """Create summary by correlation strength"""

        strength_summary = all_correlations.groupby('strength').agg({
            'correlation': 'count',
            'significant': 'sum',
            'correlation_abs': ['mean', 'min', 'max']
        }).round(3)

        strength_summary.columns = ['total_count', 'significant_count', 'avg_abs_corr', 'min_abs_corr', 'max_abs_corr']
        strength_summary['significance_rate'] = (strength_summary['significant_count'] / strength_summary['total_count'] * 100).round(1)

        # Reorder by strength
        strength_order = ['very_large', 'large', 'medium', 'small', 'negligible']
        strength_summary = strength_summary.reindex([s for s in strength_order if s in strength_summary.index])

        return strength_summary

    def create_correlation_visualizations(self, correlation_matrix: pd.DataFrame,
                                        all_correlations: pd.DataFrame) -> None:
        """Create correlation visualizations"""
        logger.info("Creating correlation visualizations...")

        # 1. Correlation heatmap
        plt.figure(figsize=(16, 12))
        mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
        sns.heatmap(correlation_matrix, mask=mask, annot=True, cmap='coolwarm',
                   center=0, square=True, fmt='.2f', cbar_kws={"shrink": .8})
        plt.title('Correlation Matrix Heatmap (Lower Triangle)', fontsize=16, pad=20)
        plt.tight_layout()
        plt.savefig(self.results_path / 'correlation_matrix_heatmap.png', dpi=300, bbox_inches='tight')
        plt.close()

        # 2. Distribution of correlation strengths
        plt.figure(figsize=(12, 6))

        plt.subplot(1, 2, 1)
        all_correlations['correlation_abs'].hist(bins=30, alpha=0.7, color='skyblue', edgecolor='black')
        plt.xlabel('Absolute Correlation')
        plt.ylabel('Frequency')
        plt.title('Distribution of Correlation Strengths')
        plt.grid(True, alpha=0.3)

        plt.subplot(1, 2, 2)
        strength_counts = all_correlations['strength'].value_counts()
        strength_counts.plot(kind='bar', color='lightcoral', alpha=0.7)
        plt.xlabel('Correlation Strength')
        plt.ylabel('Count')
        plt.title('Count by Correlation Strength Category')
        plt.xticks(rotation=45)
        plt.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(self.results_path / 'correlation_distributions.png', dpi=300, bbox_inches='tight')
        plt.close()

        # 3. Top correlations visualization
        top_20 = all_correlations.head(20)
        plt.figure(figsize=(14, 8))

        # Create labels for the plot
        labels = [f"{row['variable_1']} ↔ {row['variable_2']}" for _, row in top_20.iterrows()]

        bars = plt.barh(range(len(top_20)), top_20['correlation'],
                       color=['red' if x < 0 else 'blue' for x in top_20['correlation']], alpha=0.7)

        plt.yticks(range(len(top_20)), labels)
        plt.xlabel('Correlation Coefficient')
        plt.title('Top 20 Strongest Correlations', fontsize=14, pad=20)
        plt.grid(True, alpha=0.3, axis='x')

        # Add value labels on bars
        for i, (bar, corr) in enumerate(zip(bars, top_20['correlation'])):
            plt.text(corr + (0.01 if corr > 0 else -0.01), bar.get_y() + bar.get_height()/2,
                    f'{corr:.3f}', ha='left' if corr > 0 else 'right', va='center', fontsize=9)

        plt.tight_layout()
        plt.savefig(self.results_path / 'top_correlations.png', dpi=300, bbox_inches='tight')
        plt.close()

        logger.info("Correlation visualizations saved")

    def save_comprehensive_reports(self, correlation_matrix: pd.DataFrame,
                                 all_correlations: pd.DataFrame,
                                 detailed_reports: Dict[str, pd.DataFrame]) -> None:
        """Save all correlation reports to files"""
        logger.info("Saving comprehensive correlation reports...")

        # 1. Save correlation matrix
        correlation_matrix.to_csv(self.results_path / 'correlation_matrix.csv')
        correlation_matrix.to_excel(self.results_path / 'correlation_matrix.xlsx')

        # 2. Save all correlations
        all_correlations.to_csv(self.results_path / 'all_correlations_comprehensive.csv', index=False)
        all_correlations.to_excel(self.results_path / 'all_correlations_comprehensive.xlsx', index=False)

        # 3. Save detailed reports
        with pd.ExcelWriter(self.results_path / 'correlation_reports_detailed.xlsx') as writer:
            for report_name, report_df in detailed_reports.items():
                report_df.to_excel(writer, sheet_name=report_name, index=False)

        # 4. Save individual CSV files for each report
        for report_name, report_df in detailed_reports.items():
            report_df.to_csv(self.results_path / f'correlations_{report_name}.csv', index=False)

        logger.info("All correlation reports saved")

    def run_comprehensive_correlation_analysis(self, data: pd.DataFrame,
                                             exclude_columns: List[str] = None) -> Dict[str, Any]:
        """
        Run comprehensive correlation analysis on all features

        Args:
            data: Input dataset
            exclude_columns: List of columns to exclude from analysis

        Returns:
            Dictionary with all analysis results
        """
        logger.info("Starting comprehensive correlation analysis...")

        # Default exclude columns
        if exclude_columns is None:
            exclude_columns = ['identity', 'year_week']  # Typically non-numeric identifiers

        # 1. Analyze all pairwise correlations
        all_correlations = self.analyze_all_correlations(data, exclude_columns=exclude_columns)

        # 2. Create correlation matrix
        correlation_matrix = self.create_correlation_matrix_report(data, exclude_columns=exclude_columns)

        # 3. Create detailed reports
        detailed_reports = self.create_detailed_correlation_report(all_correlations)

        # 4. Create visualizations
        self.create_correlation_visualizations(correlation_matrix, all_correlations)

        # 5. Save all reports
        self.save_comprehensive_reports(correlation_matrix, all_correlations, detailed_reports)

        # 6. Generate summary statistics
        summary_stats = self._generate_analysis_summary(all_correlations, detailed_reports)

        logger.info("Comprehensive correlation analysis completed")

        return {
            'correlation_matrix': correlation_matrix,
            'all_correlations': all_correlations,
            'detailed_reports': detailed_reports,
            'summary_stats': summary_stats
        }

    def _generate_analysis_summary(self, all_correlations: pd.DataFrame,
                                 detailed_reports: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """Generate summary statistics for the analysis"""

        total_correlations = len(all_correlations)
        significant_correlations = len(detailed_reports['significant'])
        strong_correlations = len(detailed_reports['strong'])

        # Find strongest correlations
        strongest_positive = all_correlations[all_correlations['direction'] == 'positive'].iloc[0] if len(all_correlations[all_correlations['direction'] == 'positive']) > 0 else None
        strongest_negative = all_correlations[all_correlations['direction'] == 'negative'].iloc[0] if len(all_correlations[all_correlations['direction'] == 'negative']) > 0 else None

        return {
            'total_correlations': total_correlations,
            'significant_correlations': significant_correlations,
            'strong_correlations': strong_correlations,
            'significance_rate': (significant_correlations / total_correlations * 100) if total_correlations > 0 else 0,
            'strong_correlation_rate': (strong_correlations / total_correlations * 100) if total_correlations > 0 else 0,
            'strongest_positive': strongest_positive.to_dict() if strongest_positive is not None else None,
            'strongest_negative': strongest_negative.to_dict() if strongest_negative is not None else None,
            'avg_absolute_correlation': all_correlations['correlation_abs'].mean(),
            'median_absolute_correlation': all_correlations['correlation_abs'].median()
        }


    def generate_comprehensive_markdown_report(self, results: Dict[str, Any]) -> str:
        """Generate comprehensive markdown report"""

        summary_stats = results['summary_stats']
        all_correlations = results['all_correlations']
        detailed_reports = results['detailed_reports']

        report = f"""# COMPREHENSIVE CORRELATION ANALYSIS REPORT

## EXECUTIVE SUMMARY

Analisis korelasi komprehensif dilakukan terhadap **semua fitur numerik** dalam dataset untuk mengidentifikasi hubungan antar variabel.

### Key Statistics:
- **Total Correlations Analyzed**: {summary_stats['total_correlations']:,}
- **Significant Correlations**: {summary_stats['significant_correlations']:,} ({summary_stats['significance_rate']:.1f}%)
- **Strong Correlations (|r| ≥ 0.5)**: {summary_stats['strong_correlations']:,} ({summary_stats['strong_correlation_rate']:.1f}%)
- **Average Absolute Correlation**: {summary_stats['avg_absolute_correlation']:.3f}
- **Median Absolute Correlation**: {summary_stats['median_absolute_correlation']:.3f}

---

## STRONGEST CORRELATIONS FOUND

### All Positive Correlations:
"""

        # Add top positive correlations
        positive_corrs1 = detailed_reports['positive']
        # positive_corrs1 = positive_corrs1[positive_corrs1['correlation'] < 0.8]

        for i, (_, row) in enumerate(positive_corrs1.iterrows(), 1):
            report += f"{i}. **{row['variable_1']}** ↔ **{row['variable_2']}**: r = {row['correlation']:.3f} (p = {row['p_value']:.3f})\n"

        report += "\n### All Negative Correlations:\n"

        # Add top negative correlations
        negative_corrs1 = detailed_reports['negative']
        # negative_corrs1 = negative_corrs1[negative_corrs1['correlation'] > -0.8]

        for i, (_, row) in enumerate(negative_corrs1.iterrows(), 1):
            report += f"{i}. **{row['variable_1']}** ↔ **{row['variable_2']}**: r = {row['correlation']:.3f} (p = {row['p_value']:.3f})\n"

        report += f"""

"""
        return report


def main():
    """Enhanced main function for comprehensive correlation analysis"""
    # Load processed data
    data_path = Path("dataset/processed/weekly_merged_dataset_with_gamification.csv")

    if not data_path.exists():
        print("❌ Processed data not found. Run data_processor.py first.")
        return

    print("📊 Loading data for comprehensive correlation analysis...")
    data = pd.read_csv(data_path)

    print(f"📈 Dataset shape: {data.shape}")
    print(f"🔢 Numeric columns: {len(data.select_dtypes(include=[np.number]).columns)}")

    # Initialize analyzer
    analyzer = CorrelationAnalyzer()

    # Run comprehensive analysis
    print("\n🔍 Running comprehensive correlation analysis...")
    results = analyzer.run_comprehensive_correlation_analysis(data)

    # Generate and save markdown report
    print("\n📝 Generating comprehensive report...")
    markdown_report = analyzer.generate_comprehensive_markdown_report(results)

    report_path = analyzer.results_path / "comprehensive_correlation_report.md"
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(markdown_report)

    # Print summary
    summary_stats = results['summary_stats']
    print(f"\n✅ ANALYSIS COMPLETE!")
    print(f"   📊 Total correlations: {summary_stats['total_correlations']:,}")
    print(f"   ✨ Significant: {summary_stats['significant_correlations']:,} ({summary_stats['significance_rate']:.1f}%)")
    print(f"   💪 Strong (|r|≥0.5): {summary_stats['strong_correlations']:,} ({summary_stats['strong_correlation_rate']:.1f}%)")
    print(f"   📈 Avg |correlation|: {summary_stats['avg_absolute_correlation']:.3f}")

    if summary_stats['strongest_positive']:
        strongest_pos = summary_stats['strongest_positive']
        print(f"\n🏆 STRONGEST POSITIVE: {strongest_pos['variable_1']} ↔ {strongest_pos['variable_2']}")
        print(f"   r = {strongest_pos['correlation']:.3f}, p = {strongest_pos['p_value']:.3f}")

    if summary_stats['strongest_negative']:
        strongest_neg = summary_stats['strongest_negative']
        print(f"\n🔻 STRONGEST NEGATIVE: {strongest_neg['variable_1']} ↔ {strongest_neg['variable_2']}")
        print(f"   r = {strongest_neg['correlation']:.3f}, p = {strongest_neg['p_value']:.3f}")

    print(f"\n📁 Reports saved to: {analyzer.results_path}")
    print(f"📋 Main report: comprehensive_correlation_report.md")


if __name__ == "__main__":
    main()
