"""
Visualization Module
Clean code implementation for creating publication-ready visualizations
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import logging

logger = logging.getLogger(__name__)

class Visualizer:
    """
    Clean visualization class following SOLID principles
    """
    
    def __init__(self, output_path: str = "results/visualizations"):
        """
        Initialize visualizer
        
        Args:
            output_path: Path to save visualizations
        """
        self.output_path = Path(output_path)
        self.output_path.mkdir(parents=True, exist_ok=True)
        
        # Set style
        plt.style.use('default')
        sns.set_palette("husl")
        
        # Configure matplotlib for high quality output
        plt.rcParams['figure.dpi'] = 300
        plt.rcParams['savefig.dpi'] = 300
        plt.rcParams['font.size'] = 10
        plt.rcParams['axes.titlesize'] = 12
        plt.rcParams['axes.labelsize'] = 10
        plt.rcParams['xtick.labelsize'] = 9
        plt.rcParams['ytick.labelsize'] = 9
        plt.rcParams['legend.fontsize'] = 9
    
    def create_correlation_scatter(self, data: pd.DataFrame,
                                 x_var: str, y_var: str,
                                 title: str, filename: str,
                                 correlation: Optional[float] = None) -> None:
        """
        Create scatter plot for correlation visualization
        
        Args:
            data: Input dataset
            x_var: X-axis variable
            y_var: Y-axis variable  
            title: Plot title
            filename: Output filename
            correlation: Correlation coefficient to display
        """
        logger.info(f"Creating scatter plot: {filename}")
        
        fig, ax = plt.subplots(figsize=(10, 8))
        
        # Create scatter plot
        scatter = ax.scatter(data[x_var], data[y_var], 
                           alpha=0.6, s=50, color='steelblue')
        
        # Add trend line
        z = np.polyfit(data[x_var], data[y_var], 1)
        p = np.poly1d(z)
        ax.plot(data[x_var], p(data[x_var]), "r--", alpha=0.8, linewidth=2)
        
        # Customize plot
        ax.set_xlabel(self._format_variable_name(x_var))
        ax.set_ylabel(self._format_variable_name(y_var))
        ax.set_title(title, fontweight='bold', pad=20)
        
        # Add correlation info if provided
        if correlation is not None:
            ax.text(0.05, 0.95, f'r = {correlation:.3f}', 
                   transform=ax.transAxes, fontsize=12,
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))
        
        # Add grid
        ax.grid(True, alpha=0.3)
        
        # Save plot
        plt.tight_layout()
        plt.savefig(self.output_path / filename, bbox_inches='tight')
        plt.close()
        
        logger.info(f"Saved: {filename}")
    
    def create_comparison_plot(self, data: pd.DataFrame,
                             variables: List[str], target: str,
                             title: str, filename: str) -> None:
        """
        Create comparison plot for multiple variables
        
        Args:
            data: Input dataset
            variables: List of variables to compare
            target: Target variable
            title: Plot title
            filename: Output filename
        """
        logger.info(f"Creating comparison plot: {filename}")
        
        fig, axes = plt.subplots(1, len(variables), figsize=(15, 6))
        
        if len(variables) == 1:
            axes = [axes]
        
        for i, var in enumerate(variables):
            if var in data.columns:
                # Calculate correlation
                correlation = data[var].corr(data[target])
                
                # Create scatter plot
                axes[i].scatter(data[var], data[target], 
                              alpha=0.6, s=30, color=f'C{i}')
                
                # Add trend line
                z = np.polyfit(data[var], data[target], 1)
                p = np.poly1d(z)
                axes[i].plot(data[var], p(data[var]), "r--", alpha=0.8)
                
                # Customize subplot
                axes[i].set_xlabel(self._format_variable_name(var))
                axes[i].set_ylabel(self._format_variable_name(target))
                axes[i].set_title(f'{self._format_variable_name(var)}\nr = {correlation:.3f}')
                axes[i].grid(True, alpha=0.3)
        
        # Main title
        fig.suptitle(title, fontsize=14, fontweight='bold')
        
        # Save plot
        plt.tight_layout()
        plt.savefig(self.output_path / filename, bbox_inches='tight')
        plt.close()
        
        logger.info(f"Saved: {filename}")
    
    def create_achievement_comparison(self, data: pd.DataFrame,
                                    achievement_var: str, target_var: str,
                                    title: str, filename: str) -> None:
        """
        Create achievement level comparison plot
        
        Args:
            data: Input dataset
            achievement_var: Achievement variable
            target_var: Target variable
            title: Plot title
            filename: Output filename
        """
        logger.info(f"Creating achievement comparison: {filename}")
        
        # Create achievement categories
        data_copy = data.copy()
        data_copy['achievement_category'] = pd.cut(
            data_copy[achievement_var], 
            bins=[0, 0.33, 0.66, 1.0], 
            labels=['Low', 'Medium', 'High']
        )
        
        # Calculate means by category
        means = data_copy.groupby('achievement_category')[target_var].mean()
        counts = data_copy.groupby('achievement_category').size()
        
        fig, ax = plt.subplots(figsize=(10, 8))
        
        # Create bar plot
        bars = ax.bar(means.index, means.values, 
                     color=['#ff7f7f', '#ffbf7f', '#7fbf7f'], alpha=0.8)
        
        # Add value labels on bars
        for i, (bar, mean, count) in enumerate(zip(bars, means.values, counts.values)):
            ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                   f'{mean:.1f}\n(n={count})', ha='center', va='bottom', fontweight='bold')
        
        # Customize plot
        ax.set_xlabel('Achievement Level')
        ax.set_ylabel(self._format_variable_name(target_var))
        ax.set_title(title, fontweight='bold', pad=20)
        ax.grid(True, alpha=0.3, axis='y')
        
        # Save plot
        plt.tight_layout()
        plt.savefig(self.output_path / filename, bbox_inches='tight')
        plt.close()
        
        logger.info(f"Saved: {filename}")
    
    def create_distribution_plot(self, data: pd.DataFrame,
                               variable: str, title: str, filename: str) -> None:
        """
        Create distribution plot with statistics
        
        Args:
            data: Input dataset
            variable: Variable to plot
            title: Plot title
            filename: Output filename
        """
        logger.info(f"Creating distribution plot: {filename}")
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # Histogram
        ax1.hist(data[variable], bins=20, alpha=0.7, color='steelblue', edgecolor='black')
        ax1.set_xlabel(self._format_variable_name(variable))
        ax1.set_ylabel('Frequency')
        ax1.set_title('Distribution')
        ax1.grid(True, alpha=0.3)
        
        # Box plot
        ax2.boxplot(data[variable], vert=True)
        ax2.set_ylabel(self._format_variable_name(variable))
        ax2.set_title('Box Plot')
        ax2.grid(True, alpha=0.3)
        
        # Add statistics
        stats_text = f"""
        Mean: {data[variable].mean():.2f}
        Median: {data[variable].median():.2f}
        Std: {data[variable].std():.2f}
        Min: {data[variable].min():.2f}
        Max: {data[variable].max():.2f}
        """
        
        fig.text(0.02, 0.98, stats_text, transform=fig.transFigure, 
                fontsize=10, verticalalignment='top',
                bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.8))
        
        # Main title
        fig.suptitle(title, fontsize=14, fontweight='bold')
        
        # Save plot
        plt.tight_layout()
        plt.savefig(self.output_path / filename, bbox_inches='tight')
        plt.close()
        
        logger.info(f"Saved: {filename}")
    
    def _format_variable_name(self, var_name: str) -> str:
        """Format variable name for display"""
        
        name_mapping = {
            'total_cycles': 'Total Cycles (per week)',
            'consistency_score': 'Consistency Score',
            'activity_days': 'Activity Days (per week)',
            'avg_intensity': 'Average Intensity (km/h)',
            'total_gamification_points': 'Total Gamification Points',
            'achievement_rate': 'Achievement Rate (%)',
            'gamification_balance': 'Gamification Balance',
            'activity_points': 'Activity Points',
            'productivity_points': 'Productivity Points'
        }
        
        return name_mapping.get(var_name, var_name.replace('_', ' ').title())
    
    def create_all_visualizations(self, data: pd.DataFrame,
                                correlation_results: pd.DataFrame) -> None:
        """
        Create all standard visualizations
        
        Args:
            data: Input dataset
            correlation_results: Correlation analysis results
        """
        logger.info("Creating all visualizations...")
        
        # Get significant correlations
        significant = correlation_results[correlation_results['significant']].copy()
        
        if significant.empty:
            logger.warning("No significant correlations found for visualization")
            return
        
        # 1. Main finding: Strongest correlation
        strongest = significant.iloc[0]
        self.create_correlation_scatter(
            data, strongest['predictor_variable'], strongest['target_variable'],
            f"Main Finding: {self._format_variable_name(strongest['predictor_variable'])} vs Productivity",
            "01_main_finding.png",
            strongest['correlation']
        )
        
        # 2. Gamification effect
        gamification_vars = ['total_gamification_points', 'activity_points', 'productivity_points']
        available_gamification = [var for var in gamification_vars if var in data.columns]
        
        if available_gamification:
            self.create_comparison_plot(
                data, available_gamification[:2], 'total_cycles',
                "Gamification Effects on Productivity",
                "02_gamification_effects.png"
            )
        
        # 3. Achievement comparison
        if 'achievement_rate' in data.columns:
            self.create_achievement_comparison(
                data, 'achievement_rate', 'total_cycles',
                "Productivity by Achievement Level",
                "03_achievement_comparison.png"
            )
        
        # 4. Productivity distribution
        self.create_distribution_plot(
            data, 'total_cycles',
            "Productivity Distribution Analysis",
            "04_productivity_distribution.png"
        )
        
        logger.info("All visualizations created successfully")


def main():
    """Main function for testing visualizer"""
    # Load processed data
    data_path = Path("dataset/processed/weekly_merged_dataset_with_gamification.csv")
    results_path = Path("results/reports/significant_correlations.csv")
    
    if not data_path.exists():
        print("Processed data not found. Run data_processor.py first.")
        return
    
    if not results_path.exists():
        print("Correlation results not found. Run correlation_analyzer.py first.")
        return
    
    data = pd.read_csv(data_path)
    correlation_results = pd.read_csv(results_path)
    
    # Create visualizations
    visualizer = Visualizer()
    visualizer.create_all_visualizations(data, correlation_results)
    
    print("Visualizations created successfully!")


if __name__ == "__main__":
    main()
