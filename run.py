"""
Script untuk menjalankan analisis korelasi komprehensif
Menganalisis hubungan antara SEMUA fitur numerik dalam dataset
"""

import pandas as pd
import numpy as np
from pathlib import Path
import logging
import sys

sys.path.append('src')

from correlation_analyzer import CorrelationAnalyzer

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """
    Menjalankan analisis korelasi komprehensif untuk semua fitur
    """
    
    print("="*80)
    print("🔍 COMPREHENSIVE CORRELATION ANALYSIS")
    print("="*80)
    
    # Load data
    data_path = Path("dataset/processed/weekly_merged_dataset_with_gamification.csv")
    
    if not data_path.exists():
        print("❌ ERROR: Data file not found!")
        print(f"   Expected path: {data_path}")
        print("   Please run data_processor.py first to generate the processed dataset.")
        return
    
    print(f"📂 Loading data from: {data_path}")
    data = pd.read_csv(data_path)
    
    # Data overview
    print(f"\n📊 DATASET OVERVIEW:")
    print(f"   • Shape: {data.shape}")
    print(f"   • Total columns: {len(data.columns)}")
    
    # Identify numeric columns
    numeric_cols = data.select_dtypes(include=[np.number]).columns.tolist()
    non_numeric_cols = data.select_dtypes(exclude=[np.number]).columns.tolist()
    
    print(f"   • Numeric columns: {len(numeric_cols)}")
    print(f"   • Non-numeric columns: {len(non_numeric_cols)}")
    
    if len(numeric_cols) < 2:
        print("❌ ERROR: Need at least 2 numeric columns for correlation analysis!")
        return
    
    print(f"\n🔢 NUMERIC FEATURES TO ANALYZE:")
    for i, col in enumerate(numeric_cols, 1):
        print(f"   {i:2d}. {col}")
    
    if non_numeric_cols:
        print(f"\n📝 NON-NUMERIC COLUMNS (will be excluded):")
        for col in non_numeric_cols:
            print(f"   • {col}")
    
    # Calculate expected number of correlations
    n_features = len(numeric_cols)
    total_correlations = n_features * (n_features - 1)  # All pairs excluding self-correlation
    
    print(f"\n🧮 ANALYSIS SCOPE:")
    print(f"   • Features to analyze: {n_features}")
    print(f"   • Total pairwise correlations: {total_correlations:,}")
    print(f"   • Unique pairs (undirected): {total_correlations // 2:,}")
    
    # Initialize analyzer
    print(f"\n🚀 INITIALIZING CORRELATION ANALYZER...")
    analyzer = CorrelationAnalyzer()
    
    # Run comprehensive analysis
    print(f"\n⚡ RUNNING COMPREHENSIVE ANALYSIS...")
    print("   This may take a few moments for large datasets...")
    
    try:
        results = analyzer.run_comprehensive_correlation_analysis(
            data, 
            exclude_columns=['identity', 'year_week']  # Exclude identifier columns
        )
        
        # Extract results
        all_correlations = results['all_correlations']
        detailed_reports = results['detailed_reports']
        summary_stats = results['summary_stats']
        
        print(f"\n✅ ANALYSIS COMPLETED SUCCESSFULLY!")
        
        # Display summary statistics
        print(f"\n📈 SUMMARY STATISTICS:")
        print(f"   • Total correlations analyzed: {summary_stats['total_correlations']:,}")
        print(f"   • Significant correlations (p<0.05): {summary_stats['significant_correlations']:,}")
        print(f"   • Significance rate: {summary_stats['significance_rate']:.1f}%")
        print(f"   • Strong correlations (|r|≥0.5): {summary_stats['strong_correlations']:,}")
        print(f"   • Strong correlation rate: {summary_stats['strong_correlation_rate']:.1f}%")
        print(f"   • Average absolute correlation: {summary_stats['avg_absolute_correlation']:.3f}")
        print(f"   • Median absolute correlation: {summary_stats['median_absolute_correlation']:.3f}")
        
        # Display top findings
        if summary_stats['strongest_positive']:
            strongest_pos = summary_stats['strongest_positive']
            print(f"\n🏆 STRONGEST POSITIVE CORRELATION:")
            print(f"   • {strongest_pos['variable_1']} ↔ {strongest_pos['variable_2']}")
            print(f"   • r = {strongest_pos['correlation']:.3f}")
            print(f"   • p-value = {strongest_pos['p_value']:.3f}")
            print(f"   • Sample size = {strongest_pos['sample_size']:,}")
        
        if summary_stats['strongest_negative']:
            strongest_neg = summary_stats['strongest_negative']
            print(f"\n🔻 STRONGEST NEGATIVE CORRELATION:")
            print(f"   • {strongest_neg['variable_1']} ↔ {strongest_neg['variable_2']}")
            print(f"   • r = {strongest_neg['correlation']:.3f}")
            print(f"   • p-value = {strongest_neg['p_value']:.3f}")
            print(f"   • Sample size = {strongest_neg['sample_size']:,}")
        
        # Display correlation strength breakdown
        print(f"\n📊 CORRELATION STRENGTH BREAKDOWN:")
        strength_summary = detailed_reports['strength_summary']
        for strength, row in strength_summary.iterrows():
            print(f"   • {strength.title()}: {row['total_count']:,} correlations ({row['significance_rate']:.1f}% significant)")
        
        # Display top connected variables
        print(f"\n🌐 TOP 5 MOST CONNECTED VARIABLES:")
        var_summary = detailed_reports['variable_summary'].head(5)
        for i, (var, row) in enumerate(var_summary.iterrows(), 1):
            print(f"   {i}. {var}: {row['total_significant']:.0f} significant correlations (max |r| = {row['max_abs_correlation']:.3f})")
        
        # Generate and save comprehensive report
        print(f"\n📝 GENERATING COMPREHENSIVE REPORT...")
        markdown_report = analyzer.generate_comprehensive_markdown_report(results)
        
        report_path = analyzer.results_path / "comprehensive_correlation_report.md"
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(markdown_report)
        
        # Display files generated
        print(f"\n📁 FILES GENERATED:")
        output_files = [
            "correlation_matrix.csv",
            "correlation_matrix.xlsx", 
            "all_correlations_comprehensive.csv",
            "all_correlations_comprehensive.xlsx",
            "correlation_reports_detailed.xlsx",
            "correlations_significant.csv",
            "correlations_strong.csv",
            "correlations_medium_plus.csv",
            "correlations_top_50.csv",
            "correlations_positive.csv",
            "correlations_negative.csv",
            "correlations_variable_summary.csv",
            "correlations_strength_summary.csv",
            "correlation_matrix_heatmap.png",
            "correlation_distributions.png",
            "top_correlations.png",
            "comprehensive_correlation_report.md"
        ]
        
        for file in output_files:
            file_path = analyzer.results_path / file
            if file_path.exists():
                print(f"   ✅ {file}")
            else:
                print(f"   ❌ {file} (not generated)")
        
        # Recommendations for clustering
        print(f"\n💡 RECOMMENDATIONS FOR CLUSTERING:")
        avg_corr = summary_stats['avg_absolute_correlation']
        if avg_corr > 0.5:
            print(f"   🎯 HIGH correlation detected (avg = {avg_corr:.3f})")
            print(f"   📋 STRONGLY RECOMMEND using correlation-based distance")
            print(f"   🔗 Features show strong behavioral patterns")
        elif avg_corr > 0.3:
            print(f"   🎯 MODERATE correlation detected (avg = {avg_corr:.3f})")
            print(f"   📋 CONSIDER using correlation-based distance")
            print(f"   🔗 Features show meaningful relationships")
        else:
            print(f"   🎯 LOW correlation detected (avg = {avg_corr:.3f})")
            print(f"   📋 Euclidean distance may be sufficient")
            print(f"   🔗 Features are relatively independent")
        
        print(f"\n🎉 ANALYSIS COMPLETE!")
        print(f"📂 All results saved to: {analyzer.results_path}")
        print(f"📖 Main report: comprehensive_correlation_report.md")
        
    except Exception as e:
        logger.error(f"Error during analysis: {str(e)}")
        print(f"\n❌ ERROR: Analysis failed!")
        print(f"   {str(e)}")
        print(f"   Check the log for more details.")

if __name__ == "__main__":
    main()
