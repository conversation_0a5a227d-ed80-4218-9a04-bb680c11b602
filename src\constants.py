"""
Constants Module
Centralized constants for the entire application
"""

class DataConstants:
    """Constants for data processing"""
    DEFAULT_RAW_PATH = "dataset/raw"
    DEFAULT_PROCESSED_PATH = "dataset/processed"
    
    # Gamification constants
    MAX_ACTIVITY_POINTS = 100
    MAX_PRODUCTIVITY_POINTS = 100
    MAX_TOTAL_GAMIFICATION_POINTS = 200
    WEEKLY_DISTANCE_TARGET = 6  # km
    WEEKLY_CYCLES_TARGET = 5
    
    # File names
    OUTPUT_FILENAME = 'weekly_merged_dataset_with_gamification.csv'
    STRAVA_FILENAME = 'strava.csv'
    POMOKIT_FILENAME = 'pomokit.csv'


class AnalysisConstants:
    """Constants for correlation analysis"""
    DEFAULT_SIGNIFICANCE_LEVEL = 0.05
    MINIMUM_SAMPLE_SIZE = 10
    RESULTS_PATH = "results/reports"
    
    # Correlation strength thresholds
    VERY_LARGE_THRESHOLD = 0.7
    LARGE_THRESHOLD = 0.5
    MEDIUM_THRESHOLD = 0.3
    SMALL_THRESHOLD = 0.1
    
    # File names
    ALL_CORRELATIONS_FILE = 'all_correlations.csv'
    SIGNIFICANT_CORRELATIONS_FILE = 'significant_correlations.csv'
    MEDIATION_RESULTS_FILE = 'mediation_results.csv'
    MODERATION_RESULTS_FILE = 'moderation_results.csv'


class VisualizationConstants:
    """Constants for visualization"""
    VISUALIZATIONS_PATH = "results/visualizations"
    DPI = 300
    FIGURE_SIZE = (12, 8)
    
    # Color palette
    PRIMARY_COLOR = '#2E86AB'
    SECONDARY_COLOR = '#A23B72'
    ACCENT_COLOR = '#F18F01'
    NEUTRAL_COLOR = '#C73E1D'
    
    # File names
    MAIN_FINDING_FILE = '01_main_finding.png'
    GAMIFICATION_EFFECTS_FILE = '02_gamification_effects.png'
    ACHIEVEMENT_COMPARISON_FILE = '03_achievement_comparison.png'
    PRODUCTIVITY_DISTRIBUTION_FILE = '04_productivity_distribution.png'
    GAMIFICATION_BALANCE_MODERATION_FILE = '05_gamification_balance_moderation.png'
    ACHIEVEMENT_RATE_MODERATION_FILE = '06_achievement_rate_moderation.png'
    ACTIVITY_FREQUENCY_MODERATION_FILE = '07_activity_frequency_moderation.png'
    MODERATION_SUMMARY_FILE = '08_moderation_summary.png'


class ReportConstants:
    """Constants for report generation"""
    REPORTS_PATH = "results/reports"
    ANALYSIS_SUMMARY_FILE = 'analysis_summary.md'
    ACADEMIC_REPORT_FILE = 'academic_journal_report.md'
    SUPPLEMENTARY_MATERIALS_FILE = 'supplementary_materials.md'
    JOURNAL_GUIDE_FILE = 'journal_submission_guide.md'
